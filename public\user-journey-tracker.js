// User Journey Tracking System for Super Admin Portal
// This module tracks all user interactions and stores them in the admin document

(function() {
    'use strict';

    // Initialize tracking system
    let trackingEnabled = true;
    let sessionId = null;
    let currentPage = null;
    let sessionStartTime = null;

    // Generate unique session ID
    function generateSessionId() {
        return 'session_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11);
    }

    // Initialize tracking session
    function initializeTracking() {
        sessionId = generateSessionId();
        sessionStartTime = new Date();
        currentPage = window.location.pathname.split('/').pop() || 'unknown';
        
        console.log('User journey tracking initialized:', { sessionId, currentPage });
        
        // Track session start
        trackEvent('session_started', {
            page: currentPage,
            userAgent: navigator.userAgent,
            viewport: {
                width: window.innerWidth,
                height: window.innerHeight
            },
            referrer: document.referrer || 'direct'
        });

        // Set up navigation link monitoring
        setupNavigationTracking();
    }

    // Monitor navigation links for active state changes
    function setupNavigationTracking() {
        // Check for active nav links periodically
        setInterval(() => {
            const activeNavLink = document.querySelector('nav a.active, #navigation-drawer nav a.active, header nav a.active');
            if (activeNavLink) {
                const linkText = activeNavLink.textContent.trim().toLowerCase();
                const dataContent = activeNavLink.getAttribute('data-content');
                const trackingData = activeNavLink.getAttribute('data-track');
                
                // Track page access based on active navigation
                trackPageAccess(linkText, dataContent, trackingData);
            }
        }, 1000); // Check every second

        // Also set up mutation observer to catch immediate changes
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                    const target = mutation.target;
                    if (target.matches('nav a, #navigation-drawer nav a, header nav a') && target.classList.contains('active')) {
                        const linkText = target.textContent.trim().toLowerCase();
                        const dataContent = target.getAttribute('data-content');
                        const trackingData = target.getAttribute('data-track');
                        
                        // Track page access
                        trackPageAccess(linkText, dataContent, trackingData);
                    }
                }
            });
        });

        // Start observing
        observer.observe(document.body, {
            attributes: true,
            subtree: true,
            attributeFilter: ['class']
        });
    }

    // Track page access based on navigation (now tracks as "accessed")
    function trackPageAccess(linkText, dataContent, trackingData) {
        const pageMap = {
            'dashboard': 'dashboard',
            'assessments': 'assessments',
            'learning paths': 'learningPaths',
            'metrics': 'reports',
            'reports': 'reports',
            'invitations': 'invitations',
            'invite': 'invitations'
        };

        const featureName = pageMap[linkText] || dataContent || trackingData || linkText;
        
        // Track milestone for page access
        trackMilestone(`${featureName}_accessed`, {
            page: featureName,
            navigationMethod: 'nav_link',
            linkText: linkText,
            timestamp: new Date()
        });

        // Update feature access in userJourney (as "accessed" only)
        updateFeatureAccess(featureName, 'accessed', {
            navigationMethod: 'nav_link',
            linkText: linkText
        });
    }

    // Update feature access tracking with granular accessed vs used differentiation
    async function updateFeatureAccess(featureName, usageType = 'accessed', additionalData = {}) {
        if (!firebase.auth().currentUser) return;

        const user = firebase.auth().currentUser;
        const timestamp = firebase.firestore.FieldValue.serverTimestamp();

        try {
            const adminRef = firebase.firestore().collection('Admins').doc(user.email);
            await firebase.firestore().runTransaction(async (transaction) => {
                const adminDoc = await transaction.get(adminRef);
                
                if (adminDoc.exists) {
                    const adminData = adminDoc.data();
                    let userJourney = adminData.userJourney || {};

                    // Ensure features object exists
                    if (!userJourney.features) {
                        userJourney.features = {};
                    }

                    // Initialize feature tracking if it doesn't exist
                    if (!userJourney.features[featureName]) {
                        userJourney.features[featureName] = {
                            accessed: false,
                            used: false,
                            firstAccessed: null,
                            firstUsed: null,
                            lastAccess: null,
                            lastUsed: null,
                            accessCount: 0,
                            usageCount: 0,
                            usageDetails: []
                        };
                    }

                    const feature = userJourney.features[featureName];

                    if (usageType === 'accessed') {
                        feature.accessed = true;
                        feature.accessCount = (feature.accessCount || 0) + 1;
                        feature.lastAccess = timestamp;
                        
                        if (!feature.firstAccessed) {
                            feature.firstAccessed = timestamp;
                        }

                        // Start a feature access session record (for duration tracking)
                        if (!feature.accessSessions) feature.accessSessions = [];
                        feature.accessSessions.unshift({
                            sessionId: sessionId,
                            start: new Date(),
                            end: null,
                            duration: null,
                            metadata: additionalData || {}
                        });
                        // Keep only last 100 access sessions
                        if (feature.accessSessions.length > 100) {
                            feature.accessSessions = feature.accessSessions.slice(0, 100);
                        }
                    } else if (usageType === 'used') {
                        // Mark as both accessed and used
                        feature.accessed = true;
                        feature.used = true;
                        feature.usageCount = (feature.usageCount || 0) + 1;
                        feature.lastUsed = timestamp;
                        feature.lastAccess = timestamp;
                        
                        if (!feature.firstAccessed) {
                            feature.firstAccessed = timestamp;
                        }
                        if (!feature.firstUsed) {
                            feature.firstUsed = timestamp;
                        }

                        // Store usage details for analytics
                        if (!feature.usageDetails) feature.usageDetails = [];

                        // Attempt to close the most recent open access session for this sessionId
                        try {
                            if (feature.accessSessions && feature.accessSessions.length) {
                                const openIdx = feature.accessSessions.findIndex(s => s.sessionId === sessionId && !s.end);
                                if (openIdx !== -1) {
                                    const sessionRecord = feature.accessSessions[openIdx];
                                    sessionRecord.end = new Date();
                                    sessionRecord.duration = Math.round((sessionRecord.end - new Date(sessionRecord.start)) / 1000);
                                    // update the array entry
                                    feature.accessSessions[openIdx] = sessionRecord;
                                }
                            }
                        } catch (err) {
                            console.warn('Error closing access session for used event', err);
                        }

                        const usageRecord = {
                            timestamp: new Date(),
                            sessionId,
                            ...additionalData
                        };
                        // If we have a recently closed session with duration, attach it
                        const recent = (feature.accessSessions || []).find(s => s.sessionId === sessionId && s.duration);
                        if (recent && recent.duration) {
                            usageRecord.duration = recent.duration;
                        }

                        feature.usageDetails.unshift(usageRecord);

                        // Keep only last 50 usage details
                        if (feature.usageDetails.length > 50) {
                            feature.usageDetails = feature.usageDetails.slice(0, 50);
                        }
                    }

                    // Update the document
                    transaction.update(adminRef, {
                        userJourney: userJourney,
                        lastModified: timestamp
                    });

                    console.log(`Feature ${usageType} tracked:`, featureName, additionalData);
                }
            });
        } catch (error) {
            console.error('Error updating feature access:', error);
        }
    }

    // Track milestone achievements
    async function trackMilestone(milestoneName, milestoneData = {}) {
        if (!firebase.auth().currentUser) return;

        const user = firebase.auth().currentUser;
        const timestamp = firebase.firestore.FieldValue.serverTimestamp();

        try {
            const adminRef = firebase.firestore().collection('Admins').doc(user.email);
            await firebase.firestore().runTransaction(async (transaction) => {
                const adminDoc = await transaction.get(adminRef);
                
                if (adminDoc.exists) {
                    const adminData = adminDoc.data();
                    let userJourney = adminData.userJourney || {};

                    // Ensure milestones object exists
                    if (!userJourney.milestones) {
                        userJourney.milestones = {};
                    }

                    // Record milestone
                    userJourney.milestones[milestoneName] = {
                        achieved: true,
                        timestamp: timestamp,
                        ...milestoneData
                    };

                    // Also track as an event
                    const eventRecord = {
                        eventType: 'milestone_achieved',
                        milestoneName,
                        timestamp: new Date(),
                        sessionId,
                        ...milestoneData
                    };

                    if (!userJourney.events) userJourney.events = [];
                    userJourney.events.unshift(eventRecord);

                    // Keep only last 100 events
                    if (userJourney.events.length > 100) {
                        userJourney.events = userJourney.events.slice(0, 100);
                    }

                    // Update the document
                    transaction.update(adminRef, {
                        userJourney: userJourney,
                        lastModified: timestamp
                    });

                    console.log('Milestone tracked:', milestoneName, milestoneData);
                }
            });
        } catch (error) {
            console.error('Error tracking milestone:', error);
        }
    }

    // Main tracking function
    async function trackEvent(eventType, eventData = {}) {
        if (!trackingEnabled || !firebase.auth().currentUser) {
            return;
        }

        const user = firebase.auth().currentUser;
        const timestamp = new Date();

        const eventRecord = {
            eventType,
            timestamp: timestamp,
            sessionId,
            page: currentPage,
            userAgent: navigator.userAgent,
            data: eventData
        };

        try {
            const adminRef = firebase.firestore().collection('Admins').doc(user.email);
            
            await firebase.firestore().runTransaction(async (transaction) => {
                const adminDoc = await transaction.get(adminRef);
                
                if (adminDoc.exists) {
                    const adminData = adminDoc.data();
                    
                    // Initialize journey tracking if not exists
                    if (!adminData.userJourney) {
                        adminData.userJourney = {
                            firstLoginDate: adminData.createdAt || timestamp,
                            totalSessions: 0,
                            totalEvents: 0,
                            featuresUsed: [],
                            pages: [],
                            events: []
                        };
                    }

                    const journey = adminData.userJourney;
                    
                    // Update journey data
                    journey.totalEvents = (journey.totalEvents || 0) + 1;
                    journey.lastActivityDate = timestamp;
                    
                    // Track unique features used
                    if (eventType === 'feature_used' && eventData.feature) {
                        if (!journey.featuresUsed.includes(eventData.feature)) {
                            journey.featuresUsed.push(eventData.feature);
                        }
                    }
                    
                    // Track unique pages visited
                    if (eventType === 'page_view' && eventData.page) {
                        const pageRecord = journey.pages.find(p => p.page === eventData.page);
                        if (pageRecord) {
                            pageRecord.visitCount += 1;
                            pageRecord.lastVisit = timestamp;
                        } else {
                            journey.pages.push({
                                page: eventData.page,
                                firstVisit: timestamp,
                                lastVisit: timestamp,
                                visitCount: 1
                            });
                        }
                    }
                    
                    // Add event to events array (keep last 500 events)
                    if (!journey.events) {
                        journey.events = [];
                    }
                    journey.events.unshift(eventRecord);
                    
                    if (journey.events.length > 500) {
                        journey.events = journey.events.slice(0, 500);
                    }
                    
                    // Update session tracking for new sessions
                    if (eventType === 'session_started') {
                        journey.totalSessions = (journey.totalSessions || 0) + 1;
                        journey.lastSessionStart = timestamp;
                    }
                    
                    // Update the admin document
                    transaction.update(adminRef, {
                        userJourney: journey,
                        lastModified: firebase.firestore.FieldValue.serverTimestamp()
                    });
                }
            });
        } catch (error) {
            console.error('Error tracking user event:', error);
        }
    }

    // Track page views
    function trackPageView(pageName = null) {
        const page = pageName || currentPage;
        trackEvent('page_view', { page });
    }

    // Track feature usage (enhanced to support accessed vs used)
    function trackFeatureUsage(featureName, additionalData = {}) {
        const usageType = additionalData.usageType || 'used'; // Default to 'used' for meaningful actions
        
        trackEvent('feature_used', {
            feature: featureName,
            usageType: usageType,
            ...additionalData
        });

        // Update feature tracking based on usage type
        updateFeatureAccess(featureName, usageType, additionalData);
    }

    // Track feature access (for navigation-only events)
    function trackFeatureAccess(featureName, additionalData = {}) {
        trackEvent('feature_accessed', {
            feature: featureName,
            ...additionalData
        });

        updateFeatureAccess(featureName, 'accessed', additionalData);
    }

    // Track button clicks
    function trackButtonClick(buttonName, context = {}) {
        trackEvent('button_click', {
            button: buttonName,
            ...context
        });
    }

    // Track navigation
    function trackNavigation(from, to, method = 'click') {
        trackEvent('navigation', {
            from,
            to,
            method
        });
        currentPage = to;
    }

    // Track form submissions
    function trackFormSubmission(formName, success = true, additionalData = {}) {
        trackEvent('form_submission', {
            form: formName,
            success,
            ...additionalData
        });
    }

    // Track modal interactions
    function trackModalInteraction(modalName, action, additionalData = {}) {
        trackEvent('modal_interaction', {
            modal: modalName,
            action,
            ...additionalData
        });
    }

    // Track subscription events
    function trackSubscriptionEvent(eventType, subscriptionData = {}) {
        trackEvent('subscription_event', {
            subscriptionEventType: eventType,
            ...subscriptionData
        });
    }

    // Track search and filter usage
    function trackSearchFilter(type, query, results = null) {
        trackEvent('search_filter', {
            type,
            query,
            resultCount: results
        });
    }

    // Track time spent on page
    let pageStartTime = new Date();
    function trackTimeOnPage() {
        const timeSpent = new Date() - pageStartTime;
        trackEvent('time_on_page', {
            page: currentPage,
            timeSpent: Math.round(timeSpent / 1000) // seconds
        });
    }

    // Enhanced feature-specific tracking functions
    
    // Check if user has verified email and completed assessment (for invitations usage)
    async function checkInvitationsUsageCriteria() {
        if (!firebase.auth().currentUser) return false;
        
        const user = firebase.auth().currentUser;
        const emailVerified = user.emailVerified;
        
        try {
            const adminDoc = await firebase.firestore().collection('Admins').doc(user.email).get();
            if (!adminDoc.exists) return false;
            
            const adminData = adminDoc.data();
            
            // Check if user has completed an assessment OR sent invitations
            const hasCompletedAssessment = adminData.testCompleted || false;
            const hasAssessmentResults = adminData.results && Object.keys(adminData.results).length > 0;
            const hasEmployees = adminData.employees && adminData.employees.length > 0;
            
            // User has "used" invitations if they have verified email AND (completed assessment OR sent invitations)
            const hasUsedInvitations = emailVerified && (hasCompletedAssessment || hasAssessmentResults || hasEmployees);
            
            return {
                emailVerified,
                hasCompletedAssessment,
                hasAssessmentResults,
                hasEmployees,
                hasUsedInvitations
            };
        } catch (error) {
            console.error('Error checking invitations usage criteria:', error);
            return false;
        }
    }

    // Track invitations feature with usage criteria
    async function trackInvitationsFeature(action = 'accessed', additionalData = {}) {
        const criteria = await checkInvitationsUsageCriteria();
        
        let usageType = 'accessed';
        let usageData = { action, ...additionalData };
        
        if (criteria && criteria.hasUsedInvitations) {
            usageType = 'used';
            usageData = {
                ...usageData,
                emailVerified: criteria.emailVerified,
                hasCompletedAssessment: criteria.hasCompletedAssessment,
                hasAssessmentResults: criteria.hasAssessmentResults,
                hasEmployees: criteria.hasEmployees
            };
        }
        
        trackFeatureUsage('invitations', {
            usageType,
            ...usageData
        });
        
        // Track milestone for invitations usage if criteria met
        if (usageType === 'used' && !additionalData.skipMilestone) {
            trackMilestone('invitations_used', {
                emailVerified: criteria.emailVerified,
                hasCompletedAssessment: criteria.hasCompletedAssessment,
                hasEmployees: criteria.hasEmployees,
                timestamp: new Date()
            });
        }
    }

    // Track skills gap analysis usage (only when button is clicked)
    function trackSkillsGapAnalysis(source = 'unknown', additionalData = {}) {
        trackFeatureUsage('skillsGapAnalysis', {
            usageType: 'used', // Always 'used' when button is clicked
            source: source, // 'dashboard' or 'assessments'
            ...additionalData
        });
        
        trackMilestone('skills_gap_analysis_used', {
            source,
            timestamp: new Date(),
            ...additionalData
        });
    }

    // Track assessment completion (meaningful usage)
    function trackAssessmentCompletion(additionalData = {}) {
        trackFeatureUsage('assessments', {
            usageType: 'used',
            action: 'assessment_completed',
            ...additionalData
        });
    }

    // Track email verification milestone
    function trackEmailVerification(verified = true) {
        if (verified) {
            trackMilestone('email_verified', {
                timestamp: new Date(),
                verified: true
            });
        }
    }

    // Track invitation sending (meaningful usage)
    function trackInvitationSent(count = 1, method = 'manual', additionalData = {}) {
        trackFeatureUsage('invitations', {
            usageType: 'used',
            action: 'invitations_sent',
            count: count,
            method: method, // 'manual', 'csv', 'bulk'
            ...additionalData
        });
        
        trackMilestone('invitations_sent', {
            count,
            method,
            timestamp: new Date(),
            ...additionalData
        });
    }

    // Track session end
    function trackSessionEnd() {
        const sessionDuration = new Date() - sessionStartTime;
        trackEvent('session_ended', {
            duration: Math.round(sessionDuration / 1000), // seconds
            page: currentPage
        });
    }

    // Setup automatic tracking
    function setupAutomaticTracking() {
        // Track page visibility changes
        document.addEventListener('visibilitychange', () => {
            if (document.visibilityState === 'hidden') {
                trackTimeOnPage();
            }
        });

        // Track before unload
        window.addEventListener('beforeunload', () => {
            trackSessionEnd();
        });

        // Track clicks on tracked elements
        document.addEventListener('click', (event) => {
            const element = event.target;
            
            // Track navigation links (mark as accessed only)
            if (element.matches('a[data-content]') || element.closest('a[data-content]')) {
                const link = element.matches('a[data-content]') ? element : element.closest('a[data-content]');
                const feature = link.getAttribute('data-content');
                trackFeatureAccess(feature, { navigationMethod: 'click' });
                trackNavigation(currentPage, feature, 'navigation_click');
            }
            
            // Track skills gap analysis buttons (mark as used)
            if (element.matches('[onclick*="handleSkillsGapClick"]') || element.closest('[onclick*="handleSkillsGapClick"]')) {
                const source = currentPage || 'unknown';
                trackSkillsGapAnalysis(source, {
                    elementType: element.tagName.toLowerCase(),
                    text: element.textContent.trim().substring(0, 100)
                });
            }
            
            // Track buttons with data-track attribute
            if (element.matches('[data-track]') || element.closest('[data-track]')) {
                const button = element.matches('[data-track]') ? element : element.closest('[data-track]');
                const trackData = button.getAttribute('data-track');
                trackButtonClick(trackData, {
                    elementType: button.tagName.toLowerCase(),
                    text: button.textContent.trim().substring(0, 100)
                });
            }
            
            // Track enrollment buttons (mark as used)
            if (element.matches('.enroll-button') || element.closest('.enroll-button')) {
                trackFeatureUsage('enrollment', { 
                    usageType: 'used',
                    action: 'enroll_button_click' 
                });
            }
            
            // Track user menu interactions (mark as accessed)
            if (element.matches('#user-menu-button') || element.closest('#user-menu-button')) {
                trackFeatureUsage('user_menu', { 
                    usageType: 'accessed',
                    action: 'menu_open' 
                });
            }
        });

        // Track form submissions
        document.addEventListener('submit', (event) => {
            const form = event.target;
            const formId = form.id || form.className || 'unknown_form';
            
            // Don't track immediately, wait for success/error
            setTimeout(() => {
                trackFormSubmission(formId, true);
            }, 100);
        });
    }

    // Enhanced signup tracking
    function enhanceSignupTracking() {
        // Add journey tracking to signup process
        const originalAdminData = window.signupAdminData || {};
        
        window.signupAdminData = {
            ...originalAdminData,
            userJourney: {
                firstLoginDate: null, // Will be set on first login
                signupDate: firebase.firestore.FieldValue.serverTimestamp(),
                signupSource: window.detectLeadSource ? window.detectLeadSource() : 'unknown',
                signupUserAgent: navigator.userAgent,
                totalSessions: 0,
                totalEvents: 0,
                featuresUsed: [],
                pages: [],
                events: []
            }
        };
    }

    // Public API
    window.UserJourneyTracker = {
        initialize: initializeTracking,
        trackEvent,
        trackMilestone,
        trackPageView,
        trackFeatureUsage,
        trackFeatureAccess,
        trackButtonClick,
        trackNavigation,
        trackFormSubmission,
        trackModalInteraction,
        trackSubscriptionEvent,
        trackSearchFilter,
        trackTimeOnPage,
        trackSessionEnd,
        setupAutomaticTracking,
        enhanceSignupTracking,
        
        // Enhanced feature-specific tracking
        trackInvitationsFeature,
        trackSkillsGapAnalysis,
        trackAssessmentCompletion,
        trackEmailVerification,
        trackInvitationSent,
        checkInvitationsUsageCriteria,
        
        // Utility methods
        setTrackingEnabled: (enabled) => { trackingEnabled = enabled; },
        getCurrentSession: () => ({ sessionId, currentPage, sessionStartTime }),
        
        // Quick tracking methods (updated for granular tracking)
        trackDashboardView: () => trackFeatureAccess('dashboard'),
        trackInviteUsage: () => trackInvitationsFeature('accessed'),
        trackAssessmentView: () => trackFeatureAccess('assessments'),
        trackReportsView: () => trackFeatureAccess('reports'),
        trackLearningPathsView: () => trackFeatureAccess('learning_paths'),
        trackSubscriptionModal: (action) => trackModalInteraction('subscription_modal', action),
        trackWelcomeModal: (action) => trackModalInteraction('welcome_modal', action),
        trackTourCompletion: (status) => trackEvent('tour_completion', { status }),
        trackDemo: (action, feature) => trackEvent('demo_mode', { action, feature })
    };

    // Auto-initialize when Firebase auth state changes
    if (typeof firebase !== 'undefined') {
        firebase.auth().onAuthStateChanged((user) => {
            if (user && !sessionId) {
                setTimeout(initializeTracking, 1000); // Small delay to ensure page is ready
                setTimeout(setupAutomaticTracking, 1500);
            }
        });
    }

    console.log('User Journey Tracking system loaded');
})();
