<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Duration Tracking Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #f9f9f9;
        }
        .test-section h3 {
            margin-top: 0;
            color: #007bff;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        button.success {
            background: #28a745;
        }
        button.success:hover {
            background: #1e7e34;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .status.connected {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.disconnected {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .timer {
            font-size: 1.2em;
            font-weight: bold;
            color: #007bff;
            margin: 10px 0;
        }
        .instructions {
            background: #e7f3ff;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .instructions h3 {
            margin-top: 0;
            color: #0066cc;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Duration Tracking Test</h1>
        
        <div class="instructions">
            <h3>How to Test Duration Tracking:</h3>
            <ol>
                <li><strong>Access a Feature:</strong> Click "Access [Feature]" to start tracking time</li>
                <li><strong>Wait:</strong> Let some time pass (watch the timer)</li>
                <li><strong>Use the Feature:</strong> Click "Use [Feature]" to mark meaningful interaction and close the session</li>
                <li><strong>Check Results:</strong> Open the Super Admin Dashboard and view the user journey modal to see duration metrics</li>
            </ol>
            <p><strong>Note:</strong> Duration is only recorded when you move from "accessed" to "used" state, or when the session ends (page close/visibility change).</p>
        </div>

        <div id="connection-status" class="status disconnected">
            Firebase not connected
        </div>

        <div class="test-section">
            <h3>Dashboard Feature</h3>
            <div class="timer" id="dashboard-timer">Not tracking</div>
            <button onclick="testDashboardAccess()">Access Dashboard</button>
            <button onclick="testDashboardUsage()" class="success">Use Dashboard Feature</button>
        </div>

        <div class="test-section">
            <h3>Invitations Feature</h3>
            <div class="timer" id="invitations-timer">Not tracking</div>
            <button onclick="testInvitationsAccess()">Access Invitations</button>
            <button onclick="testInvitationsUsage()" class="success">Send Invitations</button>
        </div>

        <div class="test-section">
            <h3>Assessments Feature</h3>
            <div class="timer" id="assessments-timer">Not tracking</div>
            <button onclick="testAssessmentsAccess()">Access Assessments</button>
            <button onclick="testAssessmentUsage()" class="success">Complete Assessment</button>
        </div>

        <div class="test-section">
            <h3>Skills Gap Analysis</h3>
            <div class="timer" id="skills-timer">Not tracking</div>
            <button onclick="testSkillsGapAccess()">Access Skills Gap</button>
            <button onclick="testSkillsGapUsage()" class="success">Run Analysis</button>
        </div>

        <div class="test-section">
            <h3>Session Control</h3>
            <button onclick="closeAllSessions()" style="background: #dc3545;">Close All Open Sessions</button>
            <button onclick="viewJourneyData()" style="background: #6c757d;">View Journey Data</button>
        </div>
    </div>

    <!-- Firebase and tracking scripts -->
    <script src="https://www.gstatic.com/firebasejs/9.22.1/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.22.1/firebase-firestore-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.22.1/firebase-auth-compat.js"></script>
    <script src="firebase-config.js"></script>
    <script src="user-journey-tracker.js"></script>

    <script>
        // Track timers for each feature
        const timers = {};
        
        // Initialize Firebase connection status
        firebase.auth().onAuthStateChanged(function(user) {
            const statusElement = document.getElementById('connection-status');
            if (user) {
                statusElement.textContent = `Connected as: ${user.email}`;
                statusElement.className = 'status connected';
            } else {
                statusElement.textContent = 'Not authenticated - Please login first';
                statusElement.className = 'status disconnected';
            }
        });

        function startTimer(feature) {
            if (timers[feature]) {
                clearInterval(timers[feature].interval);
            }
            
            timers[feature] = {
                startTime: Date.now(),
                interval: setInterval(() => {
                    const elapsed = Math.floor((Date.now() - timers[feature].startTime) / 1000);
                    document.getElementById(`${feature}-timer`).textContent = `Tracking: ${formatSeconds(elapsed)}`;
                }, 1000)
            };
        }

        function stopTimer(feature) {
            if (timers[feature]) {
                clearInterval(timers[feature].interval);
                const elapsed = Math.floor((Date.now() - timers[feature].startTime) / 1000);
                document.getElementById(`${feature}-timer`).textContent = `Completed: ${formatSeconds(elapsed)}`;
                delete timers[feature];
            }
        }

        function formatSeconds(seconds) {
            const hours = Math.floor(seconds / 3600);
            const minutes = Math.floor((seconds % 3600) / 60);
            const secs = seconds % 60;
            
            if (hours > 0) return `${hours}h ${minutes}m ${secs}s`;
            if (minutes > 0) return `${minutes}m ${secs}s`;
            return `${secs}s`;
        }

        // Test functions
        function testDashboardAccess() {
            if (window.UserJourneyTracker) {
                window.UserJourneyTracker.trackFeatureAccess('dashboard');
                startTimer('dashboard');
                console.log('Dashboard access tracked');
            }
        }

        function testDashboardUsage() {
            if (window.UserJourneyTracker) {
                window.UserJourneyTracker.trackFeatureUsage('dashboard', { action: 'view_analytics' });
                stopTimer('dashboard');
                console.log('Dashboard usage tracked');
            }
        }

        function testInvitationsAccess() {
            if (window.UserJourneyTracker) {
                window.UserJourneyTracker.trackFeatureAccess('invitations');
                startTimer('invitations');
                console.log('Invitations access tracked');
            }
        }

        function testInvitationsUsage() {
            if (window.UserJourneyTracker) {
                window.UserJourneyTracker.trackInvitationsFeature('used', { count: 5, method: 'email' });
                stopTimer('invitations');
                console.log('Invitations usage tracked');
            }
        }

        function testAssessmentsAccess() {
            if (window.UserJourneyTracker) {
                window.UserJourneyTracker.trackFeatureAccess('assessments');
                startTimer('assessments');
                console.log('Assessments access tracked');
            }
        }

        function testAssessmentUsage() {
            if (window.UserJourneyTracker) {
                window.UserJourneyTracker.trackAssessmentCompletion({ assessmentType: 'skills' });
                stopTimer('assessments');
                console.log('Assessment usage tracked');
            }
        }

        function testSkillsGapAccess() {
            if (window.UserJourneyTracker) {
                window.UserJourneyTracker.trackFeatureAccess('skills_gap_analysis');
                startTimer('skills');
                console.log('Skills gap access tracked');
            }
        }

        function testSkillsGapUsage() {
            if (window.UserJourneyTracker) {
                window.UserJourneyTracker.trackSkillsGapAnalysis('dashboard');
                stopTimer('skills');
                console.log('Skills gap usage tracked');
            }
        }

        function closeAllSessions() {
            // Simulate page unload to close all sessions
            if (window.UserJourneyTracker) {
                window.dispatchEvent(new Event('beforeunload'));
                console.log('All sessions closed');
                
                // Reset timers
                Object.keys(timers).forEach(feature => {
                    stopTimer(feature);
                    document.getElementById(`${feature}-timer`).textContent = 'Not tracking';
                });
            }
        }

        function viewJourneyData() {
            // Open super admin dashboard in new tab
            window.open('super-admin-dashboard.html', '_blank');
        }
    </script>
</body>
</html>
